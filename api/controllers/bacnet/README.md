# BACnet Integration API

## Overview
This API provides functionality to fetch BACnet devices discovered through IoT integration and their configuration status in the DeJoule system.

## Endpoint

### GET /m2/sites/:siteId/bacnet-controllers/:bacnetSlaveControllerId/devices

Fetches all BACnet devices for a given BACnet slave controller with their location and configuration status.

#### Parameters
- `siteId` (string, required): Site ID
- `bacnetSlaveControllerId` (string, required): BACnet slave controller ID (deviceId of the virtual slave controller in DynamoDB)

#### Headers
- `Authorization: Bearer <token>` (required)

#### Response Format
```json
{
  "data": [
    {
      "bacnet_deviceId": "12345",
      "controllerId": "11998", // null for non-configured devices
      "location": {
        "areaId": "aj86", // For non-ibms
        "regionId": "slk0", // For non-ibms  
        "leafNodeId": "1239" // For ibms (currently null)
      },
      "bacnet_properties": {
        "name": "Honeywell DDC Controller",
        "address": "*************",
        "vendor_name": "Honey<PERSON>",
        "model_name": "DDC-1000",
        "system_status": "active",
        "description": "HVAC Controller",
        "location": "Building A - Floor 1",
        "AHU_01_present_value": "75.5",
        "AHU_01_units": "degrees_fahrenheit"
      }
    }
  ],
  "message": "BACnet devices fetched successfully"
}
```

#### Response Fields
- `bacnet_deviceId`: Unique identifier of the BACnet device from PostgreSQL
- `controllerId`: DeviceId of the third-party master controller in DynamoDB (null if not configured)
- `location`: Location information if the device is configured
  - `areaId`: Area ID for non-IBMS systems
  - `regionId`: Region ID for non-IBMS systems  
  - `leafNodeId`: Leaf node ID for IBMS systems (currently null)
- `bacnet_properties`: BACnet device properties and object values
  - Basic device info (name, address, vendor, model, etc.)
  - Dynamic properties from BACnet objects and their values

#### Error Responses

**404 Not Found**
```json
{
  "message": "BACnet slave controller not found for the given site"
}
```

**500 Server Error**
```json
{
  "message": "Failed to fetch BACnet devices",
  "error": "Error details"
}
```

## Database Schema

### PostgreSQL Tables Used
- `BMSConnector`: Links BACnet network to slave controller
- `BMSDevice`: BACnet devices discovered via N3uron gateway
- `BMSDeviceObject`: Functional objects within BMS devices
- `BMSObjectProperty`: Individual BACnet properties with values

### DynamoDB Integration
- Checks `devices` table for configured third-party master controllers
- Matches using `deviceMeta.bacnet_device_id` field
- Returns location information (areaId, regionId) for configured devices

## Usage Example

```bash
curl -X GET \
  "http://localhost:1337/m2/sites/gknmh/bacnet-controllers/12345/devices" \
  -H "Authorization: Bearer your-token-here"
```

## Notes
- Only returns devices for the specified BACnet slave controller
- Devices already configured in DynamoDB will have location and controllerId populated
- Non-configured devices will have controllerId as null and no location
- BACnet properties are dynamically built from device objects and their properties
- The API follows the existing codebase patterns for simplicity and reliability
