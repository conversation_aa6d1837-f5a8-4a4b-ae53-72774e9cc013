const bacnetService = require("../../services/bacnet/bacnet.service");

module.exports = {
  friendlyName: "Fetch BACnet Devices",
  description:
    "Fetch BACnet devices for a given BACnet slave controller with their location and configuration status",
  example: [
    'curl -X GET "http://localhost:1337/m2/sites/gknmh/bacnet-controllers/12345/devices" -H "Authorization: Bearer token"',
  ],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
      required: true,
      example: "gknmh",
      description: "Site ID",
    },
    bacnetSlaveControllerId: {
      type: "string",
      required: true,
      example: "12345",
      description:
        "BACnet slave controller ID (deviceId of the virtual slave controller in DynamoDB)",
    },
  },

  exits: {
    success: {
      description: "BACnet devices fetched successfully",
      responseType: "ok",
    },
    badRequest: {
      description: "Invalid request parameters",
      responseType: "badRequest",
    },
    serverError: {
      description: "Server error occurred",
      responseType: "serverError",
    },
    notFound: {
      description: "BACnet slave controller not found",
      responseType: "notFound",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, bacnetSlaveControllerId } = inputs;

      // Validate that the BACnet slave controller exists and belongs to the site
      const slaveController = await Devices.findOne({
        deviceId: bacnetSlaveControllerId,
        siteId: siteId,
        isSlaveController: true,
      });

      if (!slaveController) {
        return exits.notFound({
          message: "BACnet slave controller not found for the given site",
        });
      }

      // Fetch BACnet devices
      const bacnetDevices = await bacnetService.fetchBacnetDevices(siteId, bacnetSlaveControllerId);

      return exits.success({
        data: bacnetDevices,
        message: "BACnet devices fetched successfully",
      });
    } catch (error) {
      sails.log.error("[BACnet > fetch-bacnet-devices] Error:", error);
      return exits.serverError({
        message: "Failed to fetch BACnet devices",
        error: error.message,
      });
    }
  },
};
