module.exports = {
  datastore: 'postgres',
  tableName: 'BMSConnector',
  primaryKey: 'id',
  attributes: {
    id: { 
      type: 'number', 
      autoIncrement: true,
      columnName: 'id'
    },
    site_id: {
      type: 'string',
      columnType: 'varchar(255)',
      required: true,
      description: 'Site ID'
    },
    protocol: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Protocol type (e.g., BACnet)'
    },
    bms_connector_class: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'BMS connector class'
    },
    status: {
      type: 'number',
      description: 'Status of the connector'
    },
    last_sync_ts: {
      type: 'ref',
      columnType: 'timestamp without time zone',
      description: 'Last synchronization timestamp'
    },
    version: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Version of the connector'
    },
    slave_controller_id: {
      type: 'number',
      description: 'Reference to slave controller ID in DynamoDB'
    },
    createdAt: { 
      type: 'ref', 
      columnType: 'timestamp with time zone', 
      autoCreatedAt: true 
    },
    updatedAt: { 
      type: 'ref', 
      columnType: 'timestamp with time zone', 
      autoUpdatedAt: true 
    }
  }
};
