module.exports = {
  datastore: 'postgres',
  tableName: 'third_party_bms_connector',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
      columnName: 'id'
    },
    siteId: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'site_id',
      required: true,
      description: 'Site ID'
    },
    protocol: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Protocol type (e.g., BACnet)'
    },
    bmsConnectorClass: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'bms_connector_class',
      description: 'BMS connector class'
    },
    status: {
      type: 'number',
      description: 'Status of the connector'
    },
    lastSyncTs: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      columnName: 'last_sync_ts',
      description: 'Last synchronization timestamp'
    },
    version: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Version of the connector'
    },
    slaveControllerId: {
      type: 'number',
      columnName: 'slave_controller_id',
      description: 'Reference to slave controller ID in DynamoDB'
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoCreatedAt: true
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoUpdatedAt: true
    }
  }
};
