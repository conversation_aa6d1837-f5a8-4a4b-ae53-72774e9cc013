module.exports = {
  datastore: 'postgres',
  tableName: 'third_party_bms_devices',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
      columnName: 'id'
    },
    name: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Device name'
    },
    address: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Device address'
    },
    system_status: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'System status'
    },
    description: {
      type: 'string',
      columnType: 'text',
      description: 'Device description'
    },
    vendor_name: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Vendor name'
    },
    model_name: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Model name'
    },
    status: {
      type: 'number',
      description: 'Device status'
    },
    location: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Device location'
    },
    bms_connector_ref_id: {
      type: 'number',
      required: true,
      description: 'Foreign key to BMSConnector'
    },
    ref_device_id: {
      type: 'number',
      description: 'Reference device ID'
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoCreatedAt: true,
      columnName: 'createdat'
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoUpdatedAt: true,
      columnName: 'updatedat'
    }
  }
};
