module.exports = {
  datastore: 'postgres',
  tableName: 'BMSDeviceObject',
  primaryKey: 'id',
  attributes: {
    id: { 
      type: 'number', 
      autoIncrement: true,
      columnName: 'id'
    },
    name: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Object name'
    },
    description: {
      type: 'string',
      columnType: 'text',
      description: 'Object description'
    },
    address: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Object address'
    },
    status: {
      type: 'number',
      description: 'Object status'
    },
    event_state: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Event state'
    },
    out_of_service: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Out of service status'
    },
    reliability: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Reliability status'
    },
    status_flags: {
      type: 'json',
      description: 'Status flags as JSON'
    },
    protocol_native_unit: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Protocol native unit'
    },
    unit: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Unit of measurement'
    },
    device_ref_id: {
      type: 'number',
      required: true,
      description: 'Foreign key to BMSDevice'
    },
    createdAt: { 
      type: 'ref', 
      columnType: 'timestamp with time zone', 
      autoCreatedAt: true,
      columnName: 'createdat'
    },
    updatedAt: { 
      type: 'ref', 
      columnType: 'timestamp with time zone', 
      autoUpdatedAt: true,
      columnName: 'updatedat'
    }
  }
};
