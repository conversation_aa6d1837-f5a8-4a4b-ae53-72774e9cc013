module.exports = {
  datastore: 'postgres',
  tableName: 'third_party_bms_object_properties',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
      columnName: 'id'
    },
    property_tag: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Property tag'
    },
    type: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Property type'
    },
    address: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Property address'
    },
    status: {
      type: 'number',
      description: 'Property status'
    },
    protocol_native_unit: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Protocol native unit'
    },
    recent_value: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Recent value of the property'
    },
    object_ref_id: {
      type: 'number',
      required: true,
      description: 'Foreign key to BMSDeviceObject'
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoCreatedAt: true,
      columnName: 'createdat'
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoUpdatedAt: true,
      columnName: 'updatedat'
    }
  }
};
