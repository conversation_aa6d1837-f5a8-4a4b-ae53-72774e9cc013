module.exports = {
  datastore: 'postgres',
  tableName: 'third_party_bms_object_properties',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
      columnName: 'id'
    },
    propertyTag: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'property_tag',
      description: 'Property tag'
    },
    type: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Property type'
    },
    address: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Property address'
    },
    status: {
      type: 'number',
      description: 'Property status'
    },
    protocolNativeUnit: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'protocol_native_unit',
      description: 'Protocol native unit'
    },
    recentValue: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'recent_value',
      description: 'Recent value of the property'
    },
    objectRefId: {
      type: 'number',
      columnName: 'object_ref_id',
      required: true,
      description: 'Foreign key to BMSDeviceObject'
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoCreatedAt: true,
      columnName: 'createdat'
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoUpdatedAt: true,
      columnName: 'updatedat'
    }
  }
};
