/**
 * ThirdPartyBmsDeviceObjects.js
 *
 * @description :: Model representing the objects of third party BMS devices.
 */

module.exports = {
  datastore: "postgres",
  tableName: "third_party_bms_device_objects",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    name: {
      type: "string",
      required: true,
    },
    description: {
      type: "string",
    },
    address: {
      type: "string",
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
      autoCreatedAt: true,
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
      autoUpdatedAt: true,
    },
    status: {
      type: "number",
      defaultsTo: 1,
    },
    eventState: {
      type: "string",
      columnName: "event_state",
    },
    outOfService: {
      type: "boolean",
      defaultsTo: false,
      columnName: "out_of_service",
    },
    reliability: {
      type: "string",
    },
    statusFlags: {
      type: "json",
      defaultsTo: [],
      columnName: "status_flags",
    },
    protocolNativeUnit: {
      type: "string",
      columnName: "protocol_native_unit",
    },
    unit: {
      type: "string",
    },
    deviceRefId: {
      type: "number",
      required: true,
      columnName: "device_ref_id",
    },
  },
};
