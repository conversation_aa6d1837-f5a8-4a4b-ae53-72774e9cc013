/**
 * ThirdPartyBmsDevices.js
 *
 * @description :: Model representing the third party bms devices.
 */

module.exports = {
  datastore: "postgres",
  tableName: "third_party_bms_devices",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    name: {
      type: "string",
      required: true,
    },
    address: {
      type: "string",
      required: true,
    },
    systemStatus: {
      columnName: "system_status",
      type: "string",
    },
    description: {
      type: "string",
    },
    vendorName: {
      type: "string",
      columnName: "vendor_name",
    },
    modelName: {
      type: "string",
      columnName: "model_name",
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
      autoCreatedAt: true,
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
      autoUpdatedAt: true,
    },
    status: {
      type: "number",
      defaultsTo: 1,
    },
    location: {
      type: "string",
    },
    bmsConnectorRefId: {
      type: "number",
      columnName: "bms_connector_ref_id",
    },
    refDeviceId: {
      type: "string",
      columnName: "ref_device_id",
    },
  },
};
