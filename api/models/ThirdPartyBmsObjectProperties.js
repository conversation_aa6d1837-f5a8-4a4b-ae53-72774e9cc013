/**
 * ThirdpartyBmsObjectProperties.js
 *
 * @description :: Model representing the third party bms object properties.
 */

module.exports = {
  datastore: "postgres",
  tableName: "third_party_bms_object_properties",
  primaryKey: "id",
  attributes: {
    id: {
      type: "number",
      autoIncrement: true,
    },
    propertyTag: {
      type: "string",
      columnName: "property_tag",
    },
    type: {
      type: "string",
    },
    address: {
      type: "string",
    },
    createdAt: {
      type: "ref",
      columnType: "timestamp",
      autoCreatedAt: true,
    },
    updatedAt: {
      type: "ref",
      columnType: "timestamp",
      autoUpdatedAt: true,
    },
    status: {
      type: "number",
      defaultsTo: 1,
    },
    protocolNativeUnit: {
      type: "string",
      columnName: "protocol_native_unit",
    },
    recentValue: {
      type: "string",
      columnName: "recent_value",
    },
    objectRefId: {
      type: "number",
      required: true,
      columnName: "object_ref_id",
    },
  },
};
