/* eslint-disable no-underscore-dangle */
const authService = require("../services/auth/auth.public");
const cacheService = require("../services/cache/cache.service");

module.exports = async function (req, res, next) {
  if (!req.headers || !req.headers.authorization) {
    return res.forbidden({ problems: ["Unauthorized Authorization"] });
  }

  if (!req.body) req.body = {};
  let decodedAuthToken;
  const authorizationToken = req.headers.authorization;
  const [authBearer, authToken] = authorizationToken.split(" ");
  if (authBearer.toLowerCase() !== "bearer")
    return res.badRequest({ problems: ["Invalid Auth token"] });
  try {
    decodedAuthToken = authService.verifyJWTToken(authToken); // check if error thro
  } catch (e) {
    if (e.name === "TokenExpiredError") {
      return res.forbidden({ problems: ["Session Expired"] });
    }
    return res.forbidden({ problems: ["Invalid Token!"] });
  }
  const { _site: siteId, id: userId } = decodedAuthToken;

  // Check if the token exists in the Redis cache
  try {
    const isTokenValid = await cacheService.isUserAuthTokenValid(userId, siteId, authToken);
    // if (!isTokenValid) {
    //   return res.forbidden({ problems: ['Invalid or expired token'] });
    // }
  } catch (e) {
    sails.log.error("[isAuthorized] Error validating token in Redis:", e);
  }

  req.body._userMeta = decodedAuthToken;
  authService.userHasSiteAccess(userId, siteId).then((result) => {
    if (result) {
      return next();
    }
    return res.forbidden({ problems: ["You do not have access of this site. Please login again"] });
  });
};
