const deviceService = require('../device/device.service');
const siteService = require('../site/site.service');

module.exports = {
  /**
   * Fetch BACnet devices for a given BACnet slave controller
   * @param {string} siteId - Site ID
   * @param {string} bacnetSlaveControllerId - BACnet slave controller ID (deviceId in DynamoDB)
   * @returns {Array} Array of BACnet devices with location and configuration status
   */
  fetchBacnetDevices: async function (siteId, bacnetSlaveControllerId) {
    try {
      // Step 1: Check if site is IBMS to determine location structure
      const siteInfo = await siteService.findOne({ siteId, status: 1 });
      const isIBMS = siteInfo && siteInfo.industryType === 'ibms';

      // Step 2: Get BACnet connector using the model
      const connectors = await BMSConnector.find({
        slave_controller_id: parseInt(bacnetSlaveControllerId),
        site_id: siteId
      });

      if (!connectors || connectors.length === 0) {
        sails.log.warn(`No BACnet connector found for slave controller ${bacnetSlaveControllerId} in site ${siteId}`);
        return [];
      }

      const connector = connectors[0];
      const connectorId = connector.id;

      // Step 3: Get all BACnet devices using the model
      const bacnetDevices = await BMSDevice.find({
        bms_connector_ref_id: connectorId
      }).sort('id ASC');

      if (!bacnetDevices || bacnetDevices.length === 0) {
        sails.log.info(`No BACnet devices found for connector ${connectorId}`);
        return [];
      }

      // Step 4: Get device objects for each BACnet device
      const deviceIds = bacnetDevices.map(device => device.id);
      const deviceObjects = await BMSDeviceObject.find({
        device_ref_id: { in: deviceIds }
      }).sort(['device_ref_id ASC', 'id ASC']);

      // Step 5: Get properties for all objects
      const objectIds = deviceObjects.map(obj => obj.id);
      let objectProperties = [];

      if (objectIds.length > 0) {
        objectProperties = await BMSObjectProperty.find({
          object_ref_id: { in: objectIds }
        }).sort(['object_ref_id ASC', 'id ASC']);
      }

      // Step 6: Check which BACnet devices are already configured as third-party master controllers in DynamoDB
      const bacnetDeviceIds = bacnetDevices.map(device => device.id.toString());
      const configuredDevices = await this.getConfiguredBacnetDevices(siteId, bacnetDeviceIds, isIBMS);

      // Step 7: Build the response format
      const response = await this.buildBacnetDeviceResponse(
        bacnetDevices,
        deviceObjects,
        objectProperties,
        configuredDevices,
        isIBMS
      );

      return response;

    } catch (error) {
      sails.log.error('[BACnet Service] Error fetching BACnet devices:', error);
      throw error;
    }
  },

  /**
   * Get configured BACnet devices from DynamoDB
   * @param {string} siteId - Site ID
   * @param {Array} bacnetDeviceIds - Array of BACnet device IDs from PostgreSQL
   * @param {boolean} isIBMS - Whether the site is IBMS
   * @returns {Object} Map of BACnet device ID to DynamoDB device info
   */
  getConfiguredBacnetDevices: async function (siteId, bacnetDeviceIds, isIBMS) {
    try {
      // Find devices in DynamoDB where deviceMeta.bacnet_device_id matches our BACnet device IDs
      const devices = await deviceService.getDeviceListBySiteId(siteId);

      const configuredDevicesMap = {};

      for (const device of devices) {
        if (device.deviceMeta && device.deviceMeta.bacnet_device_id) {
          const bacnetDeviceId = device.deviceMeta.bacnet_device_id.toString();
          if (bacnetDeviceIds.includes(bacnetDeviceId)) {
            let locationInfo = {
              deviceId: device.deviceId,
              name: device.name,
              deviceType: device.deviceType,
              isSlaveController: device.isSlaveController,
              controllerId: device.controllerId,
            };

            if (isIBMS) {
              // For IBMS sites, get leafNodeId from the hierarchy
              locationInfo.leafNodeId = await this.getLeafNodeIdForDevice(device.deviceId, siteId);
            } else {
              // For non-IBMS sites, use areaId and regionId
              locationInfo.areaId = device.areaId;
              locationInfo.regionId = device.regionId;
            }

            configuredDevicesMap[bacnetDeviceId] = locationInfo;
          }
        }
      }

      return configuredDevicesMap;

    } catch (error) {
      sails.log.error('[BACnet Service] Error getting configured devices:', error);
      throw error;
    }
  },

  /**
   * Get leaf node ID for IBMS device from hierarchy
   * @param {string} deviceId - Device ID
   * @param {string} siteId - Site ID
   * @returns {string|null} Leaf node ID or null
   */
  getLeafNodeIdForDevice: async function (deviceId, siteId) {
    try {
      // Query the nodes table to find the leaf node for this device
      const query = `
        SELECT id as leaf_node_id
        FROM nodes
        WHERE device_id = $1 AND site_id = $2 AND level_type = 'component'
        LIMIT 1
      `;

      const result = await sails.getDatastore('postgres')
        .sendNativeQuery(query, [deviceId, siteId]);

      return result.rows && result.rows.length > 0 ? result.rows[0].leaf_node_id.toString() : null;
    } catch (error) {
      sails.log.error('[BACnet Service] Error getting leaf node ID:', error);
      return null;
    }
  },

  /**
   * Build the response format for BACnet devices
   * @param {Array} bacnetDevices - BACnet devices from PostgreSQL
   * @param {Array} deviceObjects - Device objects from PostgreSQL
   * @param {Array} objectProperties - Object properties from PostgreSQL
   * @param {Object} configuredDevices - Configured devices from DynamoDB
   * @param {boolean} isIBMS - Whether the site is IBMS
   * @returns {Array} Formatted response array
   */
  buildBacnetDeviceResponse: async function (bacnetDevices, deviceObjects, objectProperties, configuredDevices, isIBMS) {
    try {
      // Group objects by device ID
      const objectsByDevice = {};
      deviceObjects.forEach(obj => {
        if (!objectsByDevice[obj.device_ref_id]) {
          objectsByDevice[obj.device_ref_id] = [];
        }
        objectsByDevice[obj.device_ref_id].push(obj);
      });

      // Group properties by object ID
      const propertiesByObject = {};
      objectProperties.forEach(prop => {
        if (!propertiesByObject[prop.object_ref_id]) {
          propertiesByObject[prop.object_ref_id] = [];
        }
        propertiesByObject[prop.object_ref_id].push(prop);
      });

      // Build response
      const response = bacnetDevices.map(device => {
        const bacnetDeviceId = device.id.toString();
        const configuredDevice = configuredDevices[bacnetDeviceId];

        // Build BACnet properties object
        const bacnetProperties = {
          name: device.name,
          address: device.address,
          vendor_name: device.vendor_name,
          model_name: device.model_name,
          system_status: device.system_status,
          description: device.description,
          location: device.location,
        };

        // Add device objects and their properties
        const objects = objectsByDevice[device.id] || [];
        objects.forEach(obj => {
          const properties = propertiesByObject[obj.id] || [];
          properties.forEach(prop => {
            const key = `${obj.name || obj.address}_${prop.property_tag}`;
            bacnetProperties[key] = prop.recent_value || prop.address;
          });
        });

        // Build location object based on site type
        let location = null;
        if (configuredDevice) {
          if (isIBMS) {
            location = {
              areaId: null,
              regionId: null,
              leafNodeId: configuredDevice.leafNodeId
            };
          } else {
            location = {
              areaId: configuredDevice.areaId,
              regionId: configuredDevice.regionId,
              leafNodeId: null
            };
          }
        }

        return {
          bacnet_deviceId: bacnetDeviceId,
          controllerId: configuredDevice ? configuredDevice.deviceId : null,
          location: location,
          bacnet_properties: bacnetProperties,
        };
      });

      return response;

    } catch (error) {
      sails.log.error('[BACnet Service] Error building response:', error);
      throw error;
    }
  },
};
