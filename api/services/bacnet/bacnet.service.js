const deviceService = require('../device/device.service');

module.exports = {
  /**
   * Fetch BACnet devices for a given BACnet slave controller
   * @param {string} siteId - Site ID
   * @param {string} bacnetSlaveControllerId - BACnet slave controller ID (deviceId in DynamoDB)
   * @returns {Array} Array of BACnet devices with location and configuration status
   */
  fetchBacnetDevices: async function (siteId, bacnetSlaveControllerId) {
    try {
      // Step 1: Get BACnet connector from PostgreSQL using the slave controller ID
      const connectorQuery = `
        SELECT id, site_id, protocol, bms_connector_class, status, last_sync_ts, version, slave_controller_id
        FROM "BMSConnector"
        WHERE slave_controller_id = $1 AND site_id = $2
      `;
      
      const connectorResult = await sails.getDatastore('postgres')
        .sendNativeQuery(connectorQuery, [parseInt(bacnetSlaveControllerId), siteId]);

      if (!connectorResult.rows || connectorResult.rows.length === 0) {
        sails.log.warn(`No BACnet connector found for slave controller ${bacnetSlaveControllerId} in site ${siteId}`);
        return [];
      }

      const connector = connectorResult.rows[0];
      const connectorId = connector.id;

      // Step 2: Get all BACnet devices from PostgreSQL
      const devicesQuery = `
        SELECT 
          bd.id,
          bd.name,
          bd.address,
          bd.system_status,
          bd.description,
          bd.vendor_name,
          bd.model_name,
          bd.createdat,
          bd.updatedat,
          bd.status,
          bd.location,
          bd.bms_connector_ref_id,
          bd.ref_device_id
        FROM "BMSDevice" bd
        WHERE bd.bms_connector_ref_id = $1
        ORDER BY bd.id
      `;

      const devicesResult = await sails.getDatastore('postgres')
        .sendNativeQuery(devicesQuery, [connectorId]);

      if (!devicesResult.rows || devicesResult.rows.length === 0) {
        sails.log.info(`No BACnet devices found for connector ${connectorId}`);
        return [];
      }

      const bacnetDevices = devicesResult.rows;

      // Step 3: Get device objects and properties for each BACnet device
      const deviceIds = bacnetDevices.map(device => device.id);
      const deviceObjectsQuery = `
        SELECT 
          bdo.id,
          bdo.name,
          bdo.description,
          bdo.address,
          bdo.createdat,
          bdo.updatedat,
          bdo.status,
          bdo.event_state,
          bdo.out_of_service,
          bdo.reliability,
          bdo.status_flags,
          bdo.protocol_native_unit,
          bdo.unit,
          bdo.device_ref_id
        FROM "BMSDeviceObject" bdo
        WHERE bdo.device_ref_id = ANY($1)
        ORDER BY bdo.device_ref_id, bdo.id
      `;

      const objectsResult = await sails.getDatastore('postgres')
        .sendNativeQuery(deviceObjectsQuery, [deviceIds]);

      // Step 4: Get properties for all objects
      const objectIds = objectsResult.rows.map(obj => obj.id);
      let propertiesResult = { rows: [] };
      
      if (objectIds.length > 0) {
        const propertiesQuery = `
          SELECT 
            bop.id,
            bop.property_tag,
            bop.type,
            bop.address,
            bop.createdat,
            bop.updatedat,
            bop.status,
            bop.protocol_native_unit,
            bop.recent_value,
            bop.object_ref_id
          FROM "BMSObjectProperty" bop
          WHERE bop.object_ref_id = ANY($1)
          ORDER BY bop.object_ref_id, bop.id
        `;

        propertiesResult = await sails.getDatastore('postgres')
          .sendNativeQuery(propertiesQuery, [objectIds]);
      }

      // Step 5: Check which BACnet devices are already configured as third-party master controllers in DynamoDB
      const bacnetDeviceIds = bacnetDevices.map(device => device.id.toString());
      const configuredDevices = await this.getConfiguredBacnetDevices(siteId, bacnetDeviceIds);

      // Step 6: Build the response format
      const response = await this.buildBacnetDeviceResponse(
        bacnetDevices,
        objectsResult.rows,
        propertiesResult.rows,
        configuredDevices
      );

      return response;

    } catch (error) {
      sails.log.error('[BACnet Service] Error fetching BACnet devices:', error);
      throw error;
    }
  },

  /**
   * Get configured BACnet devices from DynamoDB
   * @param {string} siteId - Site ID
   * @param {Array} bacnetDeviceIds - Array of BACnet device IDs from PostgreSQL
   * @returns {Object} Map of BACnet device ID to DynamoDB device info
   */
  getConfiguredBacnetDevices: async function (siteId, bacnetDeviceIds) {
    try {
      // Find devices in DynamoDB where deviceMeta.bacnet_device_id matches our BACnet device IDs
      const devices = await deviceService.getDeviceListBySiteId(siteId);
      
      const configuredDevicesMap = {};
      
      devices.forEach(device => {
        if (device.deviceMeta && device.deviceMeta.bacnet_device_id) {
          const bacnetDeviceId = device.deviceMeta.bacnet_device_id.toString();
          if (bacnetDeviceIds.includes(bacnetDeviceId)) {
            configuredDevicesMap[bacnetDeviceId] = {
              deviceId: device.deviceId,
              areaId: device.areaId,
              regionId: device.regionId,
              name: device.name,
              deviceType: device.deviceType,
              isSlaveController: device.isSlaveController,
              controllerId: device.controllerId,
            };
          }
        }
      });

      return configuredDevicesMap;

    } catch (error) {
      sails.log.error('[BACnet Service] Error getting configured devices:', error);
      throw error;
    }
  },

  /**
   * Build the response format for BACnet devices
   * @param {Array} bacnetDevices - BACnet devices from PostgreSQL
   * @param {Array} deviceObjects - Device objects from PostgreSQL
   * @param {Array} objectProperties - Object properties from PostgreSQL
   * @param {Object} configuredDevices - Configured devices from DynamoDB
   * @returns {Array} Formatted response array
   */
  buildBacnetDeviceResponse: async function (bacnetDevices, deviceObjects, objectProperties, configuredDevices) {
    try {
      // Group objects by device ID
      const objectsByDevice = {};
      deviceObjects.forEach(obj => {
        if (!objectsByDevice[obj.device_ref_id]) {
          objectsByDevice[obj.device_ref_id] = [];
        }
        objectsByDevice[obj.device_ref_id].push(obj);
      });

      // Group properties by object ID
      const propertiesByObject = {};
      objectProperties.forEach(prop => {
        if (!propertiesByObject[prop.object_ref_id]) {
          propertiesByObject[prop.object_ref_id] = [];
        }
        propertiesByObject[prop.object_ref_id].push(prop);
      });

      // Build response
      const response = bacnetDevices.map(device => {
        const bacnetDeviceId = device.id.toString();
        const configuredDevice = configuredDevices[bacnetDeviceId];
        
        // Build BACnet properties object
        const bacnetProperties = {
          name: device.name,
          address: device.address,
          vendor_name: device.vendor_name,
          model_name: device.model_name,
          system_status: device.system_status,
          description: device.description,
          location: device.location,
        };

        // Add device objects and their properties
        const objects = objectsByDevice[device.id] || [];
        objects.forEach(obj => {
          const properties = propertiesByObject[obj.id] || [];
          properties.forEach(prop => {
            const key = `${obj.name || obj.address}_${prop.property_tag}`;
            bacnetProperties[key] = prop.recent_value || prop.address;
          });
        });

        return {
          bacnet_deviceId: bacnetDeviceId,
          controllerId: configuredDevice ? configuredDevice.deviceId : null,
          location: configuredDevice ? {
            areaId: configuredDevice.areaId,
            regionId: configuredDevice.regionId,
            leafNodeId: null, // For non-IBMS systems
          } : null,
          bacnet_properties: bacnetProperties,
        };
      });

      return response;

    } catch (error) {
      sails.log.error('[BACnet Service] Error building response:', error);
      throw error;
    }
  },
};
