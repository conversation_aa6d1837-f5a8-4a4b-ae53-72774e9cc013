const siteService = require('../site/site.service');

module.exports = {
  /**
   * Fetch BACnet devices for a given BACnet slave controller
   * @param {string} siteId - Site ID
   * @param {string} bacnetSlaveControllerId - BACnet slave controller ID (deviceId in DynamoDB)
   * @returns {Array} Array of BACnet devices with location and configuration status
   */
  fetchBacnetDevices: async function (siteId, bacnetSlaveControllerId) {
    try {
      // Step 1: Check if site is IBMS to determine location structure
      const siteInfo = await siteService.findOne({ siteId, status: 1 });
      const isIBMS = siteInfo && siteInfo.industryType === 'ibms';

      // Step 2: Get BACnet connector using the model
      const connectors = await BMSConnector.find({
        slaveControllerId: parseInt(bacnetSlaveControllerId),
        siteId: siteId
      });

      if (!connectors || connectors.length === 0) {
        sails.log.warn(`No BACnet connector found for slave controller ${bacnetSlaveControllerId} in site ${siteId}`);
        return [];
      }

      const connector = connectors[0];
      const connectorId = connector.id;

      // Step 3: Get all BACnet devices using the model
      const bacnetDevices = await BMSDevice.find({
        bmsConnectorRefId: connectorId
      }).sort('id ASC');

      if (!bacnetDevices || bacnetDevices.length === 0) {
        sails.log.info(`No BACnet devices found for connector ${connectorId}`);
        return [];
      }

      // Step 4: Get device objects for each BACnet device
      const deviceIds = bacnetDevices.map(device => device.id);
      const deviceObjects = await BMSDeviceObject.find({
        deviceRefId: { in: deviceIds }
      }).sort(['deviceRefId ASC', 'id ASC']);

      // Step 5: Get properties for all objects
      const objectIds = deviceObjects.map(obj => obj.id);
      let objectProperties = [];

      if (objectIds.length > 0) {
        objectProperties = await BMSObjectProperty.find({
          objectRefId: { in: objectIds }
        }).sort(['objectRefId ASC', 'id ASC']);
      }

      // Step 6: Get configured devices from BACnet devices
      const configuredDevices = await this.getConfiguredBacnetDevices(siteId, bacnetDevices, isIBMS);

      // Step 7: Build the response format
      const response = await this.buildBacnetDeviceResponse(
        bacnetDevices,
        deviceObjects,
        objectProperties,
        configuredDevices,
        isIBMS
      );

      return response;

    } catch (error) {
      sails.log.error('[BACnet Service] Error fetching BACnet devices:', error);
      throw error;
    }
  },

  /**
   * Get configured BACnet devices using ref_device_id from BMSDevice table
   * @param {string} siteId - Site ID
   * @param {Array} bacnetDevices - BACnet devices from PostgreSQL
   * @param {boolean} isIBMS - Whether the site is IBMS
   * @returns {Object} Map of BACnet device ID to DynamoDB device info
   */
  getConfiguredBacnetDevices: async function (siteId, bacnetDevices, isIBMS) {
    try {
      const configuredDevicesMap = {};

      for (const bacnetDevice of bacnetDevices) {
        // If refDeviceId exists, this BACnet device is connected to a DeJoule device
        if (bacnetDevice.refDeviceId) {
          try {
            // Get the DeJoule device (third-party master controller) from DynamoDB
            const dejouleDevice = await Devices.findOne({
              deviceId: bacnetDevice.refDeviceId.toString(),
              siteId: siteId
            });

            if (dejouleDevice) {
              let locationInfo = {
                deviceId: dejouleDevice.deviceId,
                name: dejouleDevice.name,
                deviceType: dejouleDevice.deviceType,
                isSlaveController: dejouleDevice.isSlaveController,
                controllerId: dejouleDevice.controllerId,
              };

              if (isIBMS) {
                // For IBMS sites, get leafNodeId from the hierarchy
                locationInfo.leafNodeId = await this.getLeafNodeIdForDevice(dejouleDevice.deviceId, siteId);
              } else {
                // For non-IBMS sites, use areaId and regionId
                locationInfo.areaId = dejouleDevice.areaId;
                locationInfo.regionId = dejouleDevice.regionId;
              }

              configuredDevicesMap[bacnetDevice.id.toString()] = locationInfo;
            }
          } catch (deviceError) {
            sails.log.warn(`[BACnet Service] Could not find DeJoule device ${bacnetDevice.refDeviceId} for BACnet device ${bacnetDevice.id}:`, deviceError);
            // Continue processing other devices
          }
        }
      }

      return configuredDevicesMap;

    } catch (error) {
      sails.log.error('[BACnet Service] Error getting configured devices via ref_device_id:', error);
      throw error;
    }
  },

  /**
   * Get leaf node ID for IBMS device from hierarchy
   * @param {string} deviceId - Device ID
   * @param {string} siteId - Site ID
   * @returns {string|null} Leaf node ID or null
   */
  getLeafNodeIdForDevice: async function (deviceId, siteId) {
    try {
      // Query the nodes table to find the leaf node for this device
      const query = `
        SELECT id as leaf_node_id
        FROM nodes
        WHERE device_id = $1 AND site_id = $2 AND level_type = 'component'
        LIMIT 1
      `;

      const result = await sails.getDatastore('postgres')
        .sendNativeQuery(query, [deviceId, siteId]);

      return result.rows && result.rows.length > 0 ? result.rows[0].leaf_node_id.toString() : null;
    } catch (error) {
      sails.log.error('[BACnet Service] Error getting leaf node ID:', error);
      return null;
    }
  },

  /**
   * Build the response format for BACnet devices
   * @param {Array} bacnetDevices - BACnet devices from PostgreSQL
   * @param {Array} deviceObjects - Device objects from PostgreSQL
   * @param {Array} objectProperties - Object properties from PostgreSQL
   * @param {Object} configuredDevices - Configured devices from DynamoDB
   * @param {boolean} isIBMS - Whether the site is IBMS
   * @returns {Array} Formatted response array
   */
  buildBacnetDeviceResponse: async function (bacnetDevices, deviceObjects, objectProperties, configuredDevices, isIBMS) {
    try {
      // Group objects by device ID
      const objectsByDevice = {};
      deviceObjects.forEach(obj => {
        if (!objectsByDevice[obj.deviceRefId]) {
          objectsByDevice[obj.deviceRefId] = [];
        }
        objectsByDevice[obj.deviceRefId].push(obj);
      });

      // Group properties by object ID
      const propertiesByObject = {};
      objectProperties.forEach(prop => {
        if (!propertiesByObject[prop.objectRefId]) {
          propertiesByObject[prop.objectRefId] = [];
        }
        propertiesByObject[prop.objectRefId].push(prop);
      });

      // Build response
      const response = bacnetDevices.map(device => {
        const bacnetDeviceId = device.id.toString();
        const configuredDevice = configuredDevices[bacnetDeviceId];

        // Build BACnet properties object
        const bacnetProperties = {
          name: device.name,
          address: device.address,
          vendor_name: device.vendorName,
          model_name: device.modelName,
          system_status: device.systemStatus,
          description: device.description,
          location: device.location,
        };

        // Add device objects and their properties
        const objects = objectsByDevice[device.id] || [];
        objects.forEach(obj => {
          const properties = propertiesByObject[obj.id] || [];
          properties.forEach(prop => {
            const key = `${obj.name || obj.address}_${prop.propertyTag}`;
            bacnetProperties[key] = prop.recentValue || prop.address;
          });
        });

        // Build location object based on site type
        let location = null;
        if (configuredDevice) {
          if (isIBMS) {
            location = {
              areaId: null,
              regionId: null,
              leafNodeId: configuredDevice.leafNodeId
            };
          } else {
            location = {
              areaId: configuredDevice.areaId,
              regionId: configuredDevice.regionId,
              leafNodeId: null
            };
          }
        }

        return {
          bacnet_deviceId: bacnetDeviceId,
          controllerId: configuredDevice ? configuredDevice.deviceId : null,
          location: location,
          bacnet_properties: bacnetProperties,
        };
      });

      return response;

    } catch (error) {
      sails.log.error('[BACnet Service] Error building response:', error);
      throw error;
    }
  },
};
