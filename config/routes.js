/**
 * Route Mappings
 * (sails.config.routes)
 *
 * Your routes tell <PERSON><PERSON> what to do each time it receives a request.
 *
 * For more information on configuring custom routes, check out:
 * https://sailsjs.com/anatomy/config/routes-js
 */

/* eslint-disable key-spacing */

module.exports.routes = {

  //  ╦ ╦╔═╗╔╗ ╔═╗╔═╗╔═╗╔═╗╔═╗
  //  ║║║║╣ ╠╩╗╠═╝╠═╣║ ╦║╣ ╚═╗
  //  ╚╩╝╚═╝╚═╝╩  ╩ ╩╚═╝╚═╝╚═╝

  'GET /m2/ping': { action: 'init/ping' },

  // 'GET /v2/'  : { action: 'recipe/recipe-dashboard' }, // THIS IS JUST TEST DASHBOARD
  // ^^^^^^^^^^^^^^^^^^^  Test/temprory routes  ^^^^^^^^^^^^^^^^^^^
  'POST /m2/database/v1/': { action: 'database/adapter' }, // JouleTrack-API Table's access point

  // ^^^^^^^^^^^^^^^^^^^  Init Service  ^^^^^^^^^^^^^^^^^^^
  'GET /m2/init/v2/jouletrack/': { action: 'init/jouletrack' },

  // ^^^^^^^^^^^^^^^^^^^  Plant Page Service  ^^^^^^^^^^^^^^^^^^^
  'GET /m2/plant/v2/type/:plantType': { action: 'plantVisualisations/find-plant-config' },
  'POST /m2/plant/v2/type/:plantType': { action: 'plantVisualisations/save-plant-config' },

  // ^^^^^^^^^^^^^^^^^^^  Recipe Service  ^^^^^^^^^^^^^^^^^^^
  'GET /m2/recipe/v2/recipe/siteId/:siteId': { action: 'recipe/find-recipe-by-siteid' },
  'GET /m2/recipe/v2/recipe/siteId/:siteId/rid/:rid': { action: 'recipe/find-recipe-by-rid' },
  'POST /m2/recipe/v2/recipe': { action: 'recipe/createrecipe' },
  'DELETE /m2/recipe/v2/recipe': { action: 'recipe/delete-recipe' },
  'PUT /m2/recipe/v2/recipe': { action: 'recipe/updaterecipe' },
  'POST /m2/recipe/v2/schedule': { action: 'recipe/createschedule' },
  'DELETE /m2/recipe/v2/schedule': { action: 'recipe/delete-schedule' },
  'GET /m2/recipe/v2/schedule/siteId/:siteId': { action: 'recipe/find-schedule-by-siteid' },
  'GET /m2/recipe/v2/schedule/siteId/:siteId/sid/:sid': { action: 'recipe/find-schedule-by-sid' },
  'POST /m2/recipe/v2/deploy': { action: 'recipe/deployrecipe' },
  'POST /m2/recipe/v2/deploySchedule': { action: 'recipe/deployschedule' },
  'POST /m2/recipe/v2/startStopRecipe': { action: 'recipe/startstop-recipe' },
  'POST /m2/recipe/v2/pause': { action: 'recipe/pause-recipe' },
  'POST /m2/recipe/v2/template': { action: 'template/create-template' },
  'GET /m2/recipe/v2/template': { action: 'template/get-template' },
  'POST /m2/recipe/v2/bulk/create': { action: 'recipe/bulk-recipe-generation' },

  // ^^^^^^^^^^^^^^^^^^^  AUTH Service  ^^^^^^^^^^^^^^^^^^^
  'POST /m2/auth/v2/login': { action: 'auth/login' },
  'POST /m2/auth/v2/issueToken': { action: 'auth/refresh-token' },
  'POST /m2/auth/v2/logout': { action: 'auth/logout' },

  // ^^^^^^^^^^^^^^^^^^^  SOCKET Service  ^^^^^^^^^^^^^^^^^^^
  'POST /m2/auth/v2/registerSocket': { action: 'socket/register-socket' },
  // 'POST /m2/auth/v2/testsocket': { action: 'socket/example' },

  // ^^^^^^^^^^^^^^^^^^^  Config  Service ^^^^^^^^^^^^^^^^^^^
  // -------------------  Devices -------------------
  'GET /m2/config/v2/devices': { action: 'device/finddevice' },
  'POST /m2/config/v2/device': { action: 'device/add-device' },
  // 'POST /m2/config/v2/devices': { action: 'device/create-device' },
  'GET /m2/config/v2/getem': { action: 'device/getem' },
  'POST /m2/config/v2/updateem': { action: 'device/updateem' },
  'POST /m2/configuration/parameters/generate': { action: 'device/generate-parameter-sheet' },
  'POST /m2/configuration/parameters/save': { action: 'device/save-parameter-sheet' },
  'PUT /m2/site/:siteId/config/v2/device/:deviceId': { action: 'device/edit-device' },

  // -------------------  Controller -------------------
  'GET /m2/config/v2/controller': { action: 'device/find-controller' },
  'POST /m2/config/v2/controllers': { action: 'device/create-controller' }, // add multiple controllers
  'PUT /m2/config/v2/controller': { action: 'device/edit-controller' },
  'DELETE /m2/config/v2/site/:siteId/controller/:controllerId': { action: 'device/delete-controller' },
  // -------------------  Pid  -------------------
  'GET /m2/config/v2/pid': { action: 'device/findpidconfig' },
  'GET /m2/config/v2/pidavailablecontrollers': { action: 'device/pidlist' },
  'POST /m2/config/v2/pid': { action: 'device/setpidconfig' },

  // -------------------  Mode  -------------------
  'POST /m2/config/v2/publishmode': { action: 'device/publishmode' },
  'POST /m2/config/v2/devices/controllerconfig': { action: 'device/initcontroller' },
  'POST /m2/component/:componentId/modes/set-mode': { action: 'component/change-mode-of-asset-control' },

  // -------------------  Driver  -------------------
  'POST /m2/config/v2/rawdriver': { action: 'driver/create-driver' },
  'POST /m2/config/v2/devicedriver': { action: 'device/create-devicedriver' },
  'POST /m2/device/v2/driver': { action: 'device/find-driver' },
  'POST /m2/driver/v2/devicelist': { action: 'driver/get-device-list' },
  'POST /m2/driver/v2/refreshparameters': { action: 'driver/refresh-parameters' },
  'GET /m2/driver/v2/devices': { action: 'driver/get-driver-devices' },

  //^^^^^^^^^^^^^^^^^^^   Component service  ^^^^^^^^^^^^^^^^^^^
  'GET /m2/component/v2/component/:siteId': { action: 'component/get-component' }, // Old Route : GET /components/:siteId
  'POST /m2/configuration/component/bulk/create': { action: 'component/bulk-component-generation' },
  'PUT /m2/component': { action: 'component/edit-component' },
  'POST /m2/v2/site/:siteId/component': { action: 'component/add-new-component' },
  'DELETE /m2/v2/site/:siteId/component/:deviceId': { action: 'component/delete-component' },

  // ^^^^^^^^^^^^^^^^^^^  Sites  Service ^^^^^^^^^^^^^^^^^^^
  'POST /m2/site/v2': { action: 'site/add-new-site' }, // Old Route : POST /v1/site
  'GET /m2/site': { action: 'site/get-all-sites' },
  'GET /m2/site/v2/site/': { action: 'site/get-site' }, // Old Route : GET /v1/site/:siteId
  'PUT /m2/site/v2/site': { action: 'site/edit-site-info' }, // Old Route: PUT /v1/site/:siteId
  'POST /m2/site/v2/area/:areaId/region': { action: 'site/add-region' }, // Old Route: POST /v1/site/:siteId/region
  'PUT /m2/site/v2/area/:areaId/region/:regionId': { action: 'site/edit-region' }, // Old Route: PUT /v1/site/:siteId/region
  'DELETE /m2/site/v2/area/:areaId/region/:regionId': { action: 'site/delete-region' }, // Old Route: PUT /v1/site/:siteId/region
  'POST /m2/site/v2/area': { action: 'site/add-area' }, // Old Route: POST /v1/site/:siteId/area
  'PUT /m2/site/v2/area/:areaId': { action: 'site/edit-area' }, // Old Route: PUT /v1/site/:siteId/area
  'DELETE /m2/site/v2/area/:areaId': { action: 'site/delete-area' }, // Old Route: PUT /v1/site/:siteId/area
  'POST /m2/site/v2/:siteId/network': { action: 'site/add-network' }, // Old Route: POST /v1/site/:siteId/network

  'POST /m2/v1/site/operation/unpublish': { action: 'site/unpublish-site' },

  // ^^^^^^^^^^^^^^^^^^^  User  Service ^^^^^^^^^^^^^^^^^^^

  'GET /m2/v2/site/:siteId/users': { action: 'user/get-site-users' },
  'GET /m2/user/v2/user': { action: 'user/get-my-user' },
  'GET /m2/user/v2/users': { action: 'user/get-all-users' },
  'POST /m2/user/v2/user': { action: 'user/create-user' },
  'PUT /m2/user/v2/user': { action: 'user/update-user' },
  'POST /m2/user/v2/deleteUser': { action: 'user/delete-user' },
  'GET /m2/user/v2/role': { action: 'user/get-role' },
  'POST /m2/user/v2/role': { action: 'user/create-role' },
  'PUT /m2/user/v2/role': { action: 'user/update-role' },
  'DELETE /m2/user/v2/role': { action: 'user/delete-role' },
  'GET /m2/user/v2/policies': { action: 'user/get-policies' },
  'POST /m2/user/v2/userPolicies': { action: 'user/get-user-policies' },
  'GET /m2/user/v2/userPref': { action: 'user/get-user-pref' },
  'PUT /m2/user/v2/userPref': { action: 'user/update-user-pref' },
  'POST /m2/user/v2/componentcard': { action: 'user/create-component-card-config' },
  'GET /m2/user/v2/componentcard/:id': { action: 'user/get-component-card-config' },
  'POST /m2/user/v2/photo': { action: 'user/upload-photo' },
  'POST /m2/user/v2/available': { action: 'user/user-available' },
  'POST /m2/user/v2/editPassword': { action: 'user/edit-password' },
  'POST /m2/user/v2/resetPassword': { action: 'user/reset-password' },
  'POST /m2/user/v2/changeSetting': { action: 'user/change-setting' },

  // ^^^^^^^^^^^^^^^^^^^  DataDevice Service ^^^^^^^^^^^^^^^^^^^
  'GET /m2/analytic/v2': { action: 'datadevice/find-datadevice' },
  'POST /m2/analytic/v2/plot/:type': { action: 'datadevice/plot-graph' },
  'POST /m2/analytic/v2/presets/ahuPerformace': { action: 'datadevice/top-worst-ahus' },
  'POST /m2/analytic/v2/presets/buildingload': { action: 'datadevice/buildingload' },
  'POST /m2/analytic/v2/presets/trh': { action: 'datadevice/trh' },
  'POST /m2/component/v2/trh': { action: 'datadevice/trh-asset-page' },
  'POST /m2/data/v2/recentdata': { action: 'datadevice/recentdata' },
  'GET /m2/site/:siteId/component/:componentId/last-data-point': { action: 'datadevice/get-component-last-data-point' },
  'POST /m2/site/:siteId/assets/last-data-point': { action: 'datadevice/get-batch-asset-last-data-point' },
  'GET /m2/site/:siteId/component/:componentId/asset-run-hour-table': { action: 'datadevice/component-page-run-hour-table' },
  'GET /m2/site/:siteId/device/:deviceId/last-asset-point': { action: 'datadevice/get-asset-last-data-point' },
  'POST /m2/data/v2/devicerunminutes': { action: 'datadevice/device-run-minutes' },
  'GET /m2/analytic/v2/runHourAnalysis': { action: 'datadevice/run-hour-analysis' },
  'GET /m2/analytic/v2/presets/runhourAnalysis': { action: 'presetsAnalytics/run-hour-analysis' },
  'GET /m2/analytic/v2/presets/pressureDropAnalysis': { action: 'presetsAnalytics/pressure-drop-analysis' },
  'GET /m2/data/:deviceId': { action: 'datadevice/raw-data' },
  'GET /m2/site/:siteId/components/run-hour-anyalysis': { action: 'datadevice/run-hour-analysis-v2' },
  'POST /m2/site/:siteId/analytics/line': { action: 'datadevice/devices-analytics-line' },
  'POST /m2/analytic/v2/presets/buildLoadPattern': { action: 'datadevice/building-load' },
  'POST /m2/site/:siteId/ahu-pattern': { action: 'datadevice/ahu-operational-pattern' },
  'POST /m2/analytics/v2/site-consumption': { action: 'datadevice/site-consumption' },

  // ^^^^^^^^^^^^^^^^^^^  DataDevice Consumption Service ^^^^^^^^^^^^^^^^^^^
  'GET /m2/consumption/v2/consumptionPageData': { action: 'datadevice/consumption-page-data' },
  'GET /m2/consumptionPageData/:groupBy': { action: 'energyConsumption/consumption-page' },

  // these 5 APIs have been temporarily integrated and need to be discontinued.
  'POST /m2/analytic/v2/line': { action: 'datadevice/line-graph' },
  'POST /m2/analytic/v2/heatmap': { action: 'datadevice/heatmap-graph' },
  'POST /m2/analytic/v2/boxplot': { action: 'datadevice/boxplot-graph' },
  'POST /m2/analytic/v2/spectral': { action: 'datadevice/spectral-graph' },
  'POST /m2/analytic/v2/customGraph': { action: 'datadevice/custom-graph' },

  // ^^^^^^^^^^^^^^^^^^^  Actions Service ^^^^^^^^^^^^^^^^^^^
  'POST /m2/control/v1/send-command': { action: 'controls/send-command-to-control-asset' },
  'GET /m2/controls/component/:componentId/controls/state': { action: 'controls/get-all-control-state-by-component' },
  'POST /m2/component/controls/state': { action: 'controls/get-all-control-state-of-all-component' },

  // ^^^^^^^^^^^^^^^^^^^  Notification Service ^^^^^^^^^^^^^^^^^^^
  'POST /m2/notification/v2/notification': { action: 'notification/create-alert' },
  'GET /m2/notification/v2/notification': { action: 'notification/find-alert' },
  // ^^^^^^^^^^^^^^^^^^^  Sync Service ^^^^^^^^^^^^^^^^^^^
  'POST /m2/sync/v2/modeChange': { action: 'sync/mode-change' },
  'POST /m2/sync/v2/syncRecipes': { action: 'sync/sync-recipe' },

  // ^^^^^^^^^^^^^^^^^^^  Baseline Service ^^^^^^^^^^^^^^^^^^^
  'GET /m2/baseline/v2/consumptionAndBaseline': { action: 'baseline/find-baseline' },
  'GET /m2/baseline/v2/site/:siteId/baseline': { action: 'baseline/find-sites-baseline' },
  'DELETE /m2/baseline/v2/site/:siteId/baseline': { action: 'baseline/delete-baseline' },
  'POST /m2/baseline/v2/site/:siteId/baseline': { action: 'baseline/create-baseline' },
  'GET /m2/baseline/v2/currentPrevNext': { action: 'baseline/current-previous-tomo-baselne' },
  'POST /m2/baseline/v2/adjustment': { action: 'baseline/create-adjustment' },
  'GET /m2/baseline/v2/adjustment': { action: 'baseline/find-adjustment' },
  'GET /m2/baseline/v2/dynamicTargets': { action: 'baseline/find-targets' },
  'POST /m2/baseline/v2/dynamicTarget': { action: 'baseline/create-dynamictarget' },
  'GET /m2/baseline/v2/prevYearConsumtion': { action: 'baseline/map-prevyear-consumption-to-this-year' },
  'GET /m2/baseline/v2/efficiencyAndBaseline': { action: 'baseline/find-baseline' },

  // ^^^^^^^^^^^^^^^^^^^  Event Service ^^^^^^^^^^^^^^^^^^^
  // 'POST /m2/event/v2/mqttEvent': { action: 'event/mqtt-event' },

  // ^^^^^^^^^^^^^^^^^^^  Modes ^^^^^^^^^^^^^^^^^^^
  'POST /m2/modes/v2/modes': { action: 'modes/find-mode' },
  'GET /m2/component/v2/:componentId/mode': { action: 'component/get-mode-by-component' },
  // ^^^^^^^^^^^^^^^^^^^  graph JouleTrack ^^^^^^^^^^^^^^^^^^^
  'POST /m2/save/v2/graph': { action: 'graph/create-graph' },
  'GET /m2/graph/v2/:id': { action: 'graph/get-graph' },
  'DELETE /m2/delete/v2/graph': { action: 'graph/delete-graph' },
  'PUT /m2/graph/v2/update': { action: 'graph/update-graph' },

  // ^^^^^^^^^^^^^^^^^^^^ Component Service ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  'POST /m2/site/:siteId/component': { action: 'component/add-component' },
  'GET /m2/component/:componentId/config': { action: 'component/get-component-config' },
  'GET /m2/site/:siteId/component/:componentId/page': { action: 'component/get-component-page-detail' },
  'POST /m2/components/get-control-relationship-by-components': { action: 'component/get-control-relationship-by-components' },
  'POST /m2/components/fetch-mode-detail-by-component-list': { action: 'component/fetch-mode-detail-by-component-list' },
  'POST /m2/components/fetch-mode-detail-by-component-list-from-jouletrack-api': { action: 'component/fetch-component-mode-details-from-jouletrack-api' },
  'POST /m2/component/:componentId/order': { action: 'component/order-controls-by-component' },
  'GET /m2/site/device-type/:deviceType/components/get-control-relationship': { action: 'component/get-control-relationship-by-device-type' },
  'GET /m2/component/get-control-relationship-by-component': { action: 'component/get-control-relationship-by-component' },
  'POST /m2/components/upload-component-files': { action: 'component/upload-component-files' },

  // ^^^^^^^^^^^^^^^^^^^  LMW specific   ^^^^^^^^^^^^^^^^^^^
  'GET /m2/dashboard/getSPC': { action: 'dashboard/lmw-spc' },
  'GET /m2/dashboard/getSPC/time': { action: 'dashboard/lmw-spc-loop' },
  // ^^^^^^^^^^^^^^^^^^^  Shift wise Product Data management ^^^^^^^^^^^^^^^^^^^
  // ^^^^^^^^^^^^^^^^^^^  Shift management ^^^^^^^^^^^^^^^^^^^
  'POST  /m2/shift/v2/shift': { action: 'shift/add-shift' },
  'GET /m2/shift/v2/shift': { action: 'shift/get-shift' },
  'DELETE /m2/shift/v2/shift/:shiftId': { action: 'shift/delete-shift' },
  // // ^^^^^^^^^^^^^^^^^^^  product management ^^^^^^^^^^^^^^^^^^^
  'POST  /m2/product/v2/product': { action: 'product/add-product' },
  // // ^^^^^^^^^^^^^^^^^^^  production data management ^^^^^^^^^^^^^^^^^^^
  'POST  /m2/production/v2/record': { action: 'productionData/add-production-data' },
  'POST  /m2/production/v2/records': { action: 'productionData/add-production-batch-data' },
  'GET   /m2/production/v2/data/available': { action: 'productionData/get-available-days' },
  'GET /m2/production/v2/production-data': { action: 'productionData/get-production-data' },
  'POST /m2/production/v2/production-data/delete': { action: 'productionData/delete-production-data' },

  // ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ Dashboard Services ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  'GET /m2/dashboard': { action: 'datadevice/dashboard' },
  'GET /m2/dashboard/:siteId': { action: 'dashboard/dashboard-consumption-loadaverage' },
  'GET /m2/dashboard/v1/card/getTonnageDelivery': { action: 'dashboard/fetch-tonnage-delivery' },
  'GET /m2/site/:siteId/getMonthlyConsumption': { action: 'energyConsumption/monthly-consumption' },
  'GET /m2/site/:siteId/dashboardEMList': { action: 'device/dashboard-em-list' },
  'GET /m2/site/:siteId/weekly-trh-pattern-graph': { action: 'dashboard/smyras-yog-weekly-trh-pattern-graph' },
  'GET /m2/site/:siteId/savings': { action: 'dashboard/fetch-savings' },

  // // ^^^^^^^^^^^^^^^^^^^  iBMS Configuration ^^^^^^^^^^^^^^^^^^^
  'POST /m2/configuration/v1/system': { action: 'systems/create-system' },
  'POST /m2/configuration/v1/system/:systemId/hierarchy': { action: 'systemNodes/create-system-definition' },

  'POST /m2/configuration/v1/site/:siteId/system/:systemId': { action: 'siteSystemMapping/system-site-mapping' },

  'POST /m2/configuration/v1/system/:systemId/node': { action: 'nodes/create-node' },
  'PUT /m2/configuration/v1/system/:systemId/node/layer': { action: 'nodes/edit-system-layer' },
  'DELETE /m2/configuration/v1/system/:systemId/node/leaf': { action: 'nodes/delete-system-leaf' },

  'GET /m2/configuration/v1/site/:siteId/system-details': { action: 'systems/fetch-system-details' },
  'POST /m2/configuration/v1/svg-tagging': { action: 'systems/upload-svg-tagging-csv' },
  'GET /m2/configuration/v1/svg-tagging': { action: 'systems/get-svg-tagging' },

  //--------------------- sunshine IBMS specific API -----------------------------------------------
  'GET /m2/system/fas/summary': { action: 'ibms/sunshine-fas-summary' },
  'GET /m2/joule-cool/line-graph-mock-api': { action: 'ibms/joule-cool-line-graph' }, //Mock API

  //-----------------Laundry Plant page ----------------------
  'GET /m2/plantPage/detail': { action: 'plantPage/plant-wise-component-param-detail' },

  // -------------Configurator Table --------------------
  'POST /m2/configurator/v1/page/:pageId/table-group/:tableGroupId/table': { action: 'configuratorTable/create-table' },
  'PUT /m2/configurator/v1/page/:pageId/table-group/:tableGroupId/table/:tableId': { action: 'configuratorTable/update-table' },
  'POST /m2/configurator/v1/page/:pageId/table-group': { action: 'configuratorTable/create-table-group' },
  'DELETE /m2/configurator/page/:pageId/table-group/:tableGroupId': { action: 'configuratorTable/delete-table-group' },
  'DELETE /m2/configurator/page/:pageId/table-group/:tgId/table/:tableId': { action: 'configuratorTable/delete-table' },
  'POST /m2/configurator/page/:pageId/table-group/:tableGroupId': { action: 'configuratorTable/update-table-group' },
  'GET /m2/configurator/page/:pageId/table-group/:tableGroupId': { action: 'configuratorTable/fetch-table' },
  'GET /m2/configurator/page/:pageId/table-group': { action: 'configuratorTable/fetch-table-group' },
  'GET /m2/configurator/:siteId/page/:pageId/get-table-details-by-page-id': { action: 'configuratorTable/get-table-details-by-page-id' },
  'GET /m2/configurator/v1/site/:siteId/page/:pageId/auto-create-table': { action: 'configuratorTable/auto-create-table' },

  // ----------------------- configurator -------------------------------------------
  'POST /m2/configurator/v1/system/category': { action: 'configurator/create-system-category' },
  'GET /m2/configurator/v1/system/category': { action: 'configurator/fetch-all-system-categories' },
  'DELETE /m2/configurator/v1/system/:systemId/category': { action: 'configurator/delete-system-category' },
  'POST /m2/configurator/v1/site/:siteId/system': { action: 'configurator/create-configurator-system' },
  'GET /m2/configurator/v1/site/:siteId/navigation': { action: 'configurator/dynamic-navigation' },
  'GET /m2/configurator/v1/site/:siteId/system': { action: 'configurator/fetch-configurator-dashboard' },
  'POST /m2/configurator/v1/site/:siteId/system/:systemId/save-system-svg': { action: 'configurator/save-system-svg' },
  'PUT /m2/configurator/v1/site/:siteId/system/:systemId/unpublish': { action: 'configurator/unpublish' },
  'PUT /m2/configurator/v1/site/:siteId/system/:systemId/publish': { action: 'configurator/publish' },
  'DELETE /m2/configurator/v1/site/:siteId/system/:systemId': { action: 'configurator/delete-configurator-system' },
  'GET /m2/configurator/v1/site/:siteId/system/:systemId': { action: 'configurator/fetch-system' },
  'PUT /m2/configurator/v1/site/:siteId/system/:systemId': { action: 'configurator/update-system' },
  'POST /m2/configurator/v1/site/:siteId/save-system-order': { action: 'configurator/save-system-order' },
  'POST /m2/configurator/v2/siteId/:siteId/subsystem/:subSystemId/page/:pageId': { action: 'configuratorPage/save-svg-page-data-tagging' },
  'POST /m2/configurator/v1/site/:siteId/page/:pageId/svg': { action: 'configuratorPage/upload-svg-for-graphic-page' },
  'GET /m2/configurator/v1/site/:siteId/system/:systemId/page/:pageId': { action: 'configuratorPage/fetch-page-detail-by-id' },
  'GET /m2/configurator/v1/site/:siteId/system/:systemId/subsystem/:subsystemId/fetch-all-page': { action: 'configurator/fetch-all-configurator-page' },
  'POST /m2/configurator/v1/siteId/:siteId/system/:systemId/data-tagging': { action: 'configurator/save-system-data-tagging' },
  'GET /m2/configurator/v1/siteId/:siteId/fetch-all-asset': { action: 'site/fetch-all-asset' },

  'GET /m2/v2/site/:siteId/asset-list-by-type': { action: 'device/asset-list-by-type' },
  'GET /m2/v2/site/:siteId/asset-parameters-list-by-type': { action: 'device/get-asset-parameter' },
  'GET /m2/site/:siteId/device-component-category': { action: 'site/fetch-all-device-components-device-type' },
  'GET /m2/v2/driver/:deviceType/parameters-with-relationship': { action: 'driver/get-driver-parameter' },

  // --- Configurator Page ------------
  'POST /m2/configurator/v1/site/:siteId/system/:systemId/subsystem/:subsystemId/page': { action: 'configuratorPage/create-configurator-page' },
  'POST /m2/configurator/v1/site/:siteId/system/:systemId/subsystem/:subsystemId/page/:pageId': { action: 'configuratorPage/update-configurator-page' },
  'DELETE /m2/configurator/v1/site/:siteId/system/:systemId/subsystem/:subsystemId/page/:pageId': { action: 'configuratorPage/delete-configurator-page' },
  'POST /m2/configurator/v1/site/:siteId/page/:pageId/publish': { action: 'configuratorPage/publish-configurator-page' },
  'POST /m2/configurator/v1/site/:siteId/page/:pageId/unpublish': { action: 'configuratorPage/unpublish-configurator-page' },
  'POST /m2/configurator/v2/page/:pageId/update-custom-config': { action: 'configuratorPage/update-page-custom-config' },
  'POST /m2/configurator/v2/site/:siteId/subsystem/:subsystemId/save-page-order': { action: 'configuratorPage/save-page-order' },

  // ---------------------- Control Config -------------------------------
  'POST /m2/control/store-command-relationship-by-driver': { action: 'controlsRelationshipConfig/store-command-relationship-by-driver' },
  'POST /m2/control/sync-controls-relationship-with-components': { action: 'controlsRelationshipConfig/sync-control-relationship-by-site' },
  'POST /m2/v1/driver/control-relationship/fetch': { action: 'controlsRelationshipConfig/fetch-control-relationship-driver' },
  'POST /m2/v1/driver/control-relationship/operation': { action: 'controlsRelationshipConfig/crud-control-relationship-by-driver' },
  'GET /m2/v1/driver/controlRelationship/:format/download': { action: 'controlsRelationshipConfig/download-driver-controls-relationship' },
  'POST /m2/v1/driver/control-relationship/component/sync': { action: 'controlsRelationshipConfig/sync-component-control-relationship' },
  'POST /m2/v1/driver/control-relationship/sync': { action: 'controlsRelationshipConfig/sync-control-relationship' },
  'POST /m2/control/clear-control-relationship-by-driver': { action: 'controlsRelationshipConfig/clear-control-relationship-by-driver' },
  'POST /m2/control/update-new-control-class': { action: 'controlsRelationshipConfig/update-new-control-class' },

  // ----------- IoT packet Handler -------------
  'POST /m2/control/jouletrack/feedback-webhook': { action: 'controls/jouletrack-command-feedback-handler-webhook' },
  // ----------- AHU(critical ahu) control by OT Panel control panel-------------
  // otcm means - OT Control Panel Mode
  'POST /m2/recipe/mode/change-setpoint-to-otcp': {
    action: 'recipe/thermal-actuator-change-setpoint-to-otcp'
  },
  'GET /m2/recipe/ahu/fetch-ot-panel-controlled-recipe': { action: 'recipe/fetch-ot-panel-controlled-recipe' },
  'POST /m2/recipe/thermal/change-setpoint': {
    action: 'recipe/change-setpoint-of-running-recipe'
  },
  // ------------- Command history --------------------------------
  'GET /m2/site/:siteId/component/:componentId/get-control-abbr': { action: 'controls/get-command-history-meta-info' },
  'GET /m2/site/:siteId/component/:componentId/controlAbbr/:controlAbbr': { action: 'controls/get-command-history' },
  'GET /m2/site/:siteId/component/:componentId/controlAbbr/:controlAbbr/fetch-last-command': { action: 'controls/fetch-last-command' },
  'POST /m2/site/:siteId/component/:componentId/recent-data-by-params': { action: 'datadevice/recent-data-over-mqtt' },

  // ---------- Configurator Graph Sankey -------------
  'GET /m2/driver/v2/get-driver-based-on-flow-parameter': { action: 'driver/get-driver-based-on-flow-parameter' },
  'POST /m2/configurator/v1/page/:pageId/create-graph-type-page': { action: 'configuratorGraph/create-graph-type-page' },
  'PUT /m2/configurator/v1/graph/:graphId': { action: 'configuratorGraph/update-graph-property' },
  'POST /m2/configurator/v1/page/:pageId/graph/:graphId': { action: 'configuratorGraph/save-graph-config' },
  'POST /m2/configurator/v2/fetch-all-flow-based-asset': { action: 'site/fetch-all-flow-based-asset' },

  /** Vigilante Specific API end Point Start * */
  'POST /m2/vigilante/v1/messageTemplate': { action: 'messageTemplate/register-new-message-template' },
  'PUT /m2/vigilante/v1/messageTemplate/:messageTemplateId/message/:messageId': { action: 'messageTemplate/update-message-by-message-id' },

  'GET /m2/vigilante/v1/messageTemplate/:templateId': { action: 'messageTemplate/get-message-template-by-id' },
  'DELETE /m2/vigilante/v1/messageTemplate/:templateId': { action: 'messageTemplate/delete-message-template' },
  'POST /m2/vigilante/v1/alert/template': { action: 'alertTemplate/create-new-alert-template' },
  'DELETE /m2/vigilante/v1/alert/template/:templateId': { action: 'alertTemplate/delete-alert-template' },
  'POST /m2/vigilante/v1/alert/template/:templateId/activate': { action: 'alertTemplate/activate-alert-template' },
  'POST /m2/vigilante/v1/alert/template/:templateId/deactivate': { action: 'alertTemplate/deactivate-alert-template' },
  'POST /m2/vigilante/v1/alert/unsubscribe': { action: 'alerts/unsubscribe-alert' },
  'DELETE /m2/vigilante/v1/alert/:alertId': { action: 'alerts/delete-alert-instance' },
  'POST /m2/vigilante/v1/alert/subscribe': { action: 'alerts/subscribe-alert' },
  /** Vigilante-Specific API end Point End* */
  'POST /m2/configurator/v1/fetch-device-data-within-interval': { action: 'configurator/fetch-device-data' },
  'GET /m2/metrics': { action: 'metrics/metrics' },

  // ---------- Maintenance Mode -------------
  'POST /m2/maintenance/:siteId/fetch-assets-maintenance-mode': { action: 'maintenanceMode/fetch-assets-maintenance-mode' },
  'POST /m2/maintenance/set-maintenance-mode': { action: 'maintenanceMode/set-maintenance-mode' },
  'GET /m2/maintenance/:siteId/get-maintenance-mode-by-deviceid': { action: 'maintenanceMode/get-maintenance-mode-by-deviceid' },
  'GET /m2/maintenance/v1/site/:siteId/asset/:assetId/fetch-maintenance-mode-history': { action: 'maintenanceMode/fetch-maintenance-mode-history' },

  // ---------- Maintenance Card -------------
  'POST /m2/v2/maintenance/card': { action: 'maintenanceCard/create-maintenance-ticket' },
  'GET /m2/v2/maintenance/cards': { action: 'maintenanceCard/fetch-maintenance-tickets' },
  'GET /m2/v2/maintenance/card/:id': { action: 'maintenanceCard/fetch-maintenance-ticket-by-id' },
  'DELETE /m2/v2/maintenance/card/:id': { action: 'maintenanceCard/delete-maintenance-ticket' },
  'PUT /m2/v2/maintenance/card/:id': { action: 'maintenanceCard/update-maintenance-ticket' },
  'GET /m2/v2/maintenance/card/:id/conversation': { action: 'maintenanceCard/fetch-conversation-by-ticket' },
  'POST /m2/v2/maintenance/card/:id/comment': { action: 'maintenanceCard/add-comment-to-conversation' },

  // ------------------- Novu Dejoule Sync APIs --------------------------------
  'POST /m2/user/novu/sync': { action: 'user/user-novu-sync' },

  // ---------- Site Bootstrap -------------
  'GET /m2/v2/site/:siteId/dejoule/bootstrap': { action: 'siteBootstrap/dejoule-site-bootstrap-data' },

  /**External Data Exchange Services */
  'GET /external/v1/siteId/:siteId/devices': { action: 'externalDataExchange/fetch-allowed-devices-list-by-params' },
  'GET /external/v1/siteId/:siteId/device/:deviceId/data': { action: 'externalDataExchange/get-time-range-recent-data' },

  // -------------------- Command Retention -------------------------------------------
  'POST /m2/site/:siteId/component/:componentId/control/command-retention': { action: 'recipe/command-retention' },
  'GET /m2/site/:siteId/component/:componentId/fetch-command-retention': { action: 'recipe/fetch-command-retention' },

  /** -------------------  Smart Alert  -------------------------------- */
  'GET /m2/v1/site/:siteId/smart-alert/alert/:alertId': { action: 'alerts/get-alertdetails-by-alertid' },
  'POST /m2/v2/smart-alert/recipe/:rid/sync': { action: 'smartAlert/sync-recipe-in-smart-alert' },
  'POST /m2/v2/site/:siteId/smart-alert/recipe/sync': { action: 'smartAlert/sync-recipe-by-site-id' },
  'POST /m2/v2/smart-alert/acknowledge/alert': { action: 'alerts/acknowledge-alert' },
  'GET /m2/v2/smart-alert/site/:siteId/alerts': { action: 'alerts/list-alerts' },
  'POST /m2/v2/smart-alert/site/:siteId/alert/:alertId/subscribe': { action: 'alerts/subscribe-smart-alert' },
  'POST /m2/v2/smart-alert/site/:siteId/alert/:alertId/unsubscribe': { action: 'alerts/unsubscribe-smart-alert' },
  'GET /m2/v2/smart-alert/site/:siteId/alert/:alertId/subscribers': { action: 'alerts/list-alert-subscribers' },
  'POST /m2/v2/smart-alert/site/:siteId/alert/:alertId/pause': { action: 'alerts/pause-smart-alert' },
  'GET /m2/v2/smart-alert/site/:siteId/alert/count-alerts': { action: 'alerts/get-total-active-alert-counter-by-siteid' },
  'GET /m2/v1/site/:siteId/alert/:alertId/alert-state': { action: 'alerts/get-alert-square-wave-graph' },

  /** ---------------------- User Config param */
  'POST /m2/site/:siteId/component/:componentId/send-request-iot-user-config-data-param': { action: 'component/send-request-iot-user-config-data-param' },
  'PUT /m2/site/:siteId/component/:componentId/user-config-data-param': { action: 'component/user-config-data-param' },

  // ---------------------------- CPA CONFIGURED DATA INGESTION ----------------------------
  'POST /m2/sync/cpa/asset-configuration': { action: 'CPAAssetConfigSync/sync-all-site-cpa-config' },
  'POST /m2/sync/cpa/site/:siteId/asset-configuration': { action: 'CPAAssetConfigSync/sync-site-asset-cpa-config' },

  'GET /m2/site/:siteId/fetch-cooling-service-charges': { action: 'dashboard/fetch-cooling-service-charges' },

  'GET /m2/site/:siteId/energy-with-tr-cost': { action: 'dashboard/smyras-yog-energy-with-tr-cost' },
  'GET /m2/site/:siteId/energy-consumption-savings': { action: 'dashboard/smyras-yog-energy-consumption-savings' },
  'GET /m2/site/:siteId/fetch-plant-run-hour-with-tr-mtd': { action: 'dashboard/fetch-plant-run-hour-with-tr-mtd' },
  'GET /m2/site/:siteId/analytics/device/:deviceId/abbr/:abbr/line-graph': { action: 'graph/get-device-param-line-graph' },


  /** -------------------  Super Recipe  -------------------------------- */
  'POST /m2/v2/site/:siteId/super-recipe': { action: 'superRecipe/create-recipe' },
  'POST /m2/v2/site/:siteId/template/super-recipe': { action: 'superRecipe/create-recipe-template' },
  'GET /m2/v2/site/:siteId/super-recipe/:id': { action: 'superRecipe/fetch-recipe-by-id' },
  'GET /m2/v2/site/:siteId/super-recipe/template/:id': { action: 'superRecipe/fetch-recipe-template-by-id' },
  'PUT /m2/v2/site/:siteId/super-recipe/:id': { action: 'superRecipe/edit-recipe-by-id' },
  'DELETE /m2/v2/site/:siteId/super-recipe/:id': { action: 'superRecipe/delete-recipe-by-id' },
  'DELETE /m2/v2/site/:siteId/super-recipe/template/:id': { action: 'superRecipe/delete-recipe-template-by-id' },
  'GET /m2/v2/site/:siteId/super-recipe': { action: 'superRecipe/fetch-super-recipes' },
  'GET /m2/v2/site/:siteId/super-recipe/template': { action: 'superRecipe/fetch-super-recipe-templates' },
  'POST /m2/v2/site/:siteId/super-recipe/:id/deploy': { action: 'superRecipe/deploy-super-recipe' },
  'POST /m2/v2/site/:siteId/super-recipe/:id/state': { action: 'superRecipe/manage-running-state' },
  'POST /m2/v2/site/:siteId/super-recipe/:recipeId/schedules': { action: 'superRecipe/create-schedules' },
  'PUT /m2/v2/site/:siteId/super-recipe/:recipeId/schedules': { action: 'superRecipe/edit-schedules' },
  'DELETE /m2/v2/site/:siteId/super-recipe/:recipeId/schedule/:scheduleId': { action: 'superRecipe/delete-schedule-by-id' },
  'POST /m2/v2/site/:siteId/super-recipe/feedback': { action: 'superRecipe/handle-recipe-feedback' },

  'POST /m2/v1/site/:siteId/recipe/super/alert/:rid/sync': { action: 'superRecipe/sync-alert-recipe-to-smart-alert' },
  'POST /m2/v1/site/:siteId/recipe/super/alert/:global/sync': { action: 'superRecipe/sync-alert-recipe-to-smart-alert' },

  'GET /m2/v1/site/:siteId/recipe/super/alert/list': { action: 'superRecipe/fetch-super-alert-recipes' },
  'GET /m2/v1/site/:siteId/recipe/super/alert/:id': { action: 'superRecipe/fetch-super-alert-recipe-detail-by-id' },

  'GET /m2/v1/site/:siteId/recipe/super/action/list': { action: 'superRecipe/fetch-super-action-recipes' },
  'GET /m2/v1/site/:siteId/recipe/super/action/:id': { action: 'superRecipe/fetch-super-action-recipe-detail-by-id' },

  'POST /m2/v2/site/:siteId/super-recipe/sync-recipe-alert': { action: 'superRecipe/sync-recipe-alert' },

  /**Microservice Health check Url */
  'GET /health': { action: 'init/ping' },

  /** BACnet Integration APIs */
  "GET /m2/site/:siteId/bacnet/:bacnetSlaveControllerId/devices": { action: "bacnet/fetch-bacnet-devices" },
  "GET /m2/site/:siteId/master/controllers/slave/:secondaryControllerId": { action: "bacnet/fetch-configured-bacnet-devices" },
  "GET /m2/site/:siteId/controller/:id/objects": { action: "bacnet/fetch-bacnet-objects" },
}
