var assert = require('assert');
var mocha = require('mocha');
var sinon = require('sinon');
sails = require('sails');
const bacnetService = require('../../../../api/services/bacnet/bacnet.service');

sinon = sinon.createSandbox();

if (mocha.describe) {
  describe = mocha.describe;
  it = mocha.it;
  before = mocha.before;
  after = mocha.after;
} else {
  describe = global.describe;
  it = global.it;
  before = global.before;
  after = global.after;
}

let fetchBacnetDevices = require('../../../../api/controllers/bacnet/fetch-bacnet-devices');

describe('fetch BACnet devices positive suite', async () => {
  let mockDevicesFindOne;
  let mockBacnetService;

  before(() => {
    // Mock the global Devices model
    global.Devices = {
      findOne: sinon.stub()
    };
    mockDevicesFindOne = global.Devices.findOne;
    mockBacnetService = sinon.stub(bacnetService, 'fetchBacnetDevices');
  });

  after(() => {
    sinon.restore();
    delete global.Devices;
  });

  it('should fetch BACnet devices successfully', async () => {
    // Mock slave controller exists
    mockDevicesFindOne.resolves({
      deviceId: '12345',
      siteId: 'gknmh',
      isSlaveController: true,
      controllerId: '11000'
    });

    // Mock BACnet service response
    const mockBacnetDevices = [
      {
        bacnet_deviceId: '12345',
        controllerId: '11998',
        location: {
          areaId: 'aj86',
          regionId: 'slk0',
          leafNodeId: null
        },
        bacnet_properties: {
          name: 'Test Device',
          address: '*************',
          vendor_name: 'Honeywell'
        }
      }
    ];
    mockBacnetService.resolves(mockBacnetDevices);

    try {
      let inputs = {
        _userMeta: { id: 'user1', _role: 'admin', _site: 'gknmh' },
        siteId: 'gknmh',
        bacnetSlaveControllerId: '12345'
      };
      let exits = {};
      exits.success = sinon.spy();
      exits.notFound = sinon.spy();
      exits.serverError = sinon.spy();

      await fetchBacnetDevices.fn(inputs, exits);

      assert.equal(exits.success.callCount, 1);
      assert.equal(exits.notFound.callCount, 0);
      assert.equal(exits.serverError.callCount, 0);

      const successCall = exits.success.getCall(0);
      assert.equal(successCall.args[0].length, 1);
      assert.equal(successCall.args[0][0].bacnet_deviceId, '12345');
    } catch (err) {
      console.log(err);
      assert.fail('Exception in fetch BACnet devices');
    }
  });
});

describe('fetch BACnet devices negative suite', async () => {
  let mockDevicesFindOne;
  let mockBacnetService;

  before(() => {
    // Mock the global Devices model
    global.Devices = {
      findOne: sinon.stub()
    };
    mockDevicesFindOne = global.Devices.findOne;
    mockBacnetService = sinon.stub(bacnetService, 'fetchBacnetDevices');
  });

  after(() => {
    sinon.restore();
    delete global.Devices;
  });

  it('should return not found when slave controller does not exist', async () => {
    // Mock slave controller not found
    mockDevicesFindOne.resolves(null);

    try {
      let inputs = {
        _userMeta: { id: 'user1', _role: 'admin', _site: 'gknmh' },
        siteId: 'gknmh',
        bacnetSlaveControllerId: '99999'
      };
      let exits = {};
      exits.success = sinon.spy();
      exits.notFound = sinon.spy();
      exits.serverError = sinon.spy();

      await fetchBacnetDevices.fn(inputs, exits);

      assert.equal(exits.success.callCount, 0);
      assert.equal(exits.notFound.callCount, 1);
      assert.equal(exits.serverError.callCount, 0);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in fetch BACnet devices negative test');
    }
  });

  it('should return server error when service throws exception', async () => {
    // Mock slave controller exists
    mockDevicesFindOne.resolves({
      deviceId: '12345',
      siteId: 'gknmh',
      isSlaveController: true
    });

    // Mock service throws error
    mockBacnetService.rejects(new Error('Database connection failed'));

    try {
      let inputs = {
        _userMeta: { id: 'user1', _role: 'admin', _site: 'gknmh' },
        siteId: 'gknmh',
        bacnetSlaveControllerId: '12345'
      };
      let exits = {};
      exits.success = sinon.spy();
      exits.notFound = sinon.spy();
      exits.serverError = sinon.spy();

      await fetchBacnetDevices.fn(inputs, exits);

      assert.equal(exits.success.callCount, 0);
      assert.equal(exits.notFound.callCount, 0);
      assert.equal(exits.serverError.callCount, 1);
    } catch (err) {
      console.log(err);
      assert.fail('Exception in fetch BACnet devices error test');
    }
  });
});
